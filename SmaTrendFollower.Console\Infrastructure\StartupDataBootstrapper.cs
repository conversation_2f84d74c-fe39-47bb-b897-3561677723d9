using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Services;
using SmaTrendFollower.Scheduling;

namespace SmaTrendFollower.Infrastructure;

/// <summary>
/// Hosted service that ensures universe data is available on cold starts
/// Checks for stale or missing universe data and refreshes it if needed
/// </summary>
public sealed class StartupDataBootstrapper : IHostedService
{
    private readonly IDatabase _db;
    private readonly IUniverseFetcherService _fetcher;
    private readonly IDynamicUniverseFilterJob _filter;
    private readonly ILogger<StartupDataBootstrapper> _log;

    private static readonly TimeSpan FullUniverseStaleness = TimeSpan.FromDays(6);
    private static readonly TimeSpan DailyUniverseStaleness = TimeSpan.FromHours(4);

    public StartupDataBootstrapper(
        ConnectionMultiplexer mux,
        IUniverseFetcherService fetcher,
        IDynamicUniverseFilterJob filter,
        ILogger<StartupDataBootstrapper> log)
    {
        _db = mux.GetDatabase();
        _fetcher = fetcher;
        _filter = filter;
        _log = log;
    }

    public async Task StartAsync(CancellationToken ct)
    {
        _log.LogInformation("Starting universe data bootstrap check");

        try
        {
            // Add timeout to prevent hanging
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(ct);
            timeoutCts.CancelAfter(TimeSpan.FromMinutes(2)); // 2-minute timeout

            // 1. Ensure full universe
            var fullTtl = await _db.KeyTimeToLiveAsync("universe:candidates");
            if (fullTtl is null || fullTtl < FullUniverseStaleness)
            {
                _log.LogWarning("Full universe missing or stale – rebuilding now (with 2-minute timeout)");
                try
                {
                    await _fetcher.RefreshAsync(timeoutCts.Token);
                }
                catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested && !ct.IsCancellationRequested)
                {
                    _log.LogWarning("Universe refresh timed out after 2 minutes - continuing with stale data");
                }
            }
            else
            {
                _log.LogInformation("Full universe is fresh (TTL: {TTL})", fullTtl);
            }

            // 2. Ensure today's filtered universe
            var todayTtl = await _db.KeyTimeToLiveAsync("universe:today");
            if (todayTtl is null || todayTtl < DailyUniverseStaleness)
            {
                _log.LogWarning("Daily universe missing or stale – filtering now (with 2-minute timeout)");
                try
                {
                    await _filter.RunOnceAsync(timeoutCts.Token);
                }
                catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested && !ct.IsCancellationRequested)
                {
                    _log.LogWarning("Universe filtering timed out after 2 minutes - continuing with stale data");
                }
            }
            else
            {
                _log.LogInformation("Daily universe is fresh (TTL: {TTL})", todayTtl);
            }

            _log.LogInformation("Startup bootstrap complete.");
        }
        catch (Exception ex)
        {
            _log.LogError(ex, "Error during startup data bootstrap");
            // Don't throw - allow the application to start even if bootstrap fails
            // The scheduled jobs will handle the refresh later
        }
    }

    public Task StopAsync(CancellationToken _) => Task.CompletedTask;
}
